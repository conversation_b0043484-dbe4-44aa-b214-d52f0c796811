/// 排盘类型枚举
enum ChartType {
  bazi('八字排盘'),
  l<PERSON><PERSON><PERSON>('六爻排盘'),
  zi<PERSON>('紫微排盘'),
  qimen('奇门排盘'),
  liu<PERSON>('六壬排盘');

  const ChartType(this.displayName);
  final String displayName;
}

/// 排盘模式枚举
enum ChartMode {
  domestic('国内排盘'),
  international('国际排盘'),
  direct('直接排盘');

  const ChartMode(this.displayName);
  final String displayName;
}

/// 性别枚举
enum Gender {
  male('男'),
  female('女');

  const Gender(this.displayName);
  final String displayName;
}

/// 历法类型枚举
enum CalendarType {
  lunar('农历'),
  solar('阳历');

  const CalendarType(this.displayName);
  final String displayName;
}

/// 子时规则枚举
enum ZiTimeRule {
  modern('夜子时(当代常用)'),
  traditional('子时换日(传统古法)');

  const ZiTimeRule(this.displayName);
  final String displayName;

  String get description {
    switch (this) {
      case ZiTimeRule.modern:
        return '当日23:00-00:00出生:\n日柱取当日干支，时柱按次日日干推算(五鼠规则)';
      case ZiTimeRule.traditional:
        return '当日23:00-24:00出生:\n日柱取次日千支，时柱按次日日干推算';
    }
  }
}

/// 天干枚举
enum TianGan {
  jia('甲'),
  yi('乙'),
  bing('丙'),
  ding('丁'),
  wu('戊'),
  ji('己'),
  geng('庚'),
  xin('辛'),
  ren('壬'),
  gui('癸');

  const TianGan(this.displayName);
  final String displayName;

  static List<String> get allNames => TianGan.values.map((e) => e.displayName).toList();
}

/// 地支枚举
enum DiZhi {
  zi('子'),
  chou('丑'),
  yin('寅'),
  mao('卯'),
  chen('辰'),
  si('巳'),
  wu('午'),
  wei('未'),
  shen('申'),
  you('酉'),
  xu('戌'),
  hai('亥');

  const DiZhi(this.displayName);
  final String displayName;

  static List<String> get allNames => DiZhi.values.map((e) => e.displayName).toList();
}
