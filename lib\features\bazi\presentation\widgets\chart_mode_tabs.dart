import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';

/// 排盘模式选择标签页组件
class ChartModeTabs extends StatelessWidget {
  const ChartModeTabs({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.dividerColor),
      ),
      child: Consumer<ChartProvider>(
        builder: (context, provider, child) {
          return Row(
            children: ChartMode.values.map((mode) {
              final isSelected = provider.currentChartMode == mode;
              final isFirst = mode == ChartMode.values.first;
              final isLast = mode == ChartMode.values.last;
              
              return Expanded(
                child: _ChartModeTab(
                  mode: mode,
                  isSelected: isSelected,
                  isFirst: isFirst,
                  isLast: isLast,
                  onTap: () => provider.setChartMode(mode),
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }
}

/// 单个排盘模式标签
class _ChartModeTab extends StatelessWidget {
  final ChartMode mode;
  final bool isSelected;
  final bool isFirst;
  final bool isLast;
  final VoidCallback onTap;

  const _ChartModeTab({
    required this.mode,
    required this.isSelected,
    required this.isFirst,
    required this.isLast,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected 
              ? theme.colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.only(
            topLeft: isFirst ? const Radius.circular(11) : Radius.zero,
            bottomLeft: isFirst ? const Radius.circular(11) : Radius.zero,
            topRight: isLast ? const Radius.circular(11) : Radius.zero,
            bottomRight: isLast ? const Radius.circular(11) : Radius.zero,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getModeIcon(mode),
              size: 18,
              color: isSelected 
                  ? theme.colorScheme.onPrimary
                  : theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(width: 6),
            Text(
              mode.displayName,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected 
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurface.withOpacity(0.8),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取排盘模式对应的图标
  IconData _getModeIcon(ChartMode mode) {
    switch (mode) {
      case ChartMode.domestic:
        return Icons.location_on_outlined;
      case ChartMode.international:
        return Icons.public_outlined;
      case ChartMode.direct:
        return Icons.edit_outlined;
    }
  }
}
