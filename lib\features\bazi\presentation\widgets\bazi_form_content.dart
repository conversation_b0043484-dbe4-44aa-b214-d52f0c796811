import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';
import 'domestic_chart_form.dart';
import 'international_chart_form.dart';
import 'direct_chart_form.dart';
import 'chart_action_buttons.dart';

/// 八字排盘表单内容组件
class BaziFormContent extends StatelessWidget {
  const BaziFormContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 根据当前模式显示不同的表单
            _buildFormContent(provider.currentChartMode),
            
            const SizedBox(height: 32),
            
            // 操作按钮区域
            const ChartActionButtons(),
          ],
        );
      },
    );
  }

  /// 根据排盘模式构建表单内容
  Widget _buildFormContent(ChartMode mode) {
    switch (mode) {
      case ChartMode.domestic:
        return const DomesticChartForm();
      case ChartMode.international:
        return const InternationalChartForm();
      case ChartMode.direct:
        return const DirectChartForm();
    }
  }
}
