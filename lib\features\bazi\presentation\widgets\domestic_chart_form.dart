import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';
import 'form_section.dart';
import 'custom_text_field.dart';
import 'custom_radio_group.dart';
import 'custom_checkbox.dart';
import 'custom_switch.dart';
import 'location_selector.dart';
import 'zi_time_rule_selector.dart';

/// 国内排盘表单组件
class DomesticChartForm extends StatelessWidget {
  const DomesticChartForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        final formData = provider.formData;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 基础信息区域
            FormSection(
              title: '基础信息',
              icon: Icons.person_outline,
              children: [
                // 姓名输入
                CustomTextField(
                  label: '姓名',
                  value: formData.name,
                  onChanged: (value) {
                    provider.updateFormData(formData.copyWith(name: value));
                  },
                  errorText: provider.getFieldError('name'),
                  isRequired: true,
                ),
                
                const SizedBox(height: 16),
                
                // 性别选择
                CustomRadioGroup<Gender>(
                  label: '性别',
                  value: formData.gender,
                  options: Gender.values,
                  optionLabels: Gender.values.map((e) => e.displayName).toList(),
                  onChanged: (value) {
                    provider.updateFormData(formData.copyWith(gender: value));
                  },
                  errorText: provider.getFieldError('gender'),
                  isRequired: true,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 出生信息区域
            FormSection(
              title: '出生信息',
              icon: Icons.calendar_today_outlined,
              children: [
                // 历法选择
                CustomRadioGroup<CalendarType>(
                  label: '历法',
                  value: formData.calendarType,
                  options: CalendarType.values,
                  optionLabels: CalendarType.values.map((e) => e.displayName).toList(),
                  onChanged: (value) {
                    provider.updateFormData(formData.copyWith(calendarType: value));
                  },
                  isRequired: true,
                ),
                
                const SizedBox(height: 16),
                
                // 出生日期时间
                _buildDateTimeInputs(context, provider, formData),
                
                // 农历闰月选项
                if (formData.calendarType == CalendarType.lunar) ...[
                  const SizedBox(height: 16),
                  CustomCheckbox(
                    label: '是否闰月',
                    value: formData.isLeapMonth,
                    onChanged: (value) {
                      provider.updateFormData(formData.copyWith(isLeapMonth: value));
                    },
                  ),
                ],
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 地址信息区域
            FormSection(
              title: '地址信息',
              icon: Icons.location_on_outlined,
              children: [
                // 地址输入开关
                CustomSwitch(
                  label: '启用地址输入',
                  subtitle: '开启后可进行真太阳时校准',
                  value: formData.enableLocationInput,
                  onChanged: (value) {
                    provider.updateFormData(formData.copyWith(enableLocationInput: value));
                  },
                ),
                
                // 地址选择器
                if (formData.enableLocationInput) ...[
                  const SizedBox(height: 16),
                  LocationSelector(
                    mode: LocationMode.domestic,
                    province: formData.province,
                    city: formData.city,
                    district: formData.district,
                    onProvinceChanged: (value) {
                      provider.updateFormData(formData.copyWith(
                        province: value,
                        city: null,
                        district: null,
                      ));
                    },
                    onCityChanged: (value) {
                      provider.updateFormData(formData.copyWith(
                        city: value,
                        district: null,
                      ));
                    },
                    onDistrictChanged: (value) {
                      provider.updateFormData(formData.copyWith(district: value));
                    },
                    provinceError: provider.getFieldError('province'),
                    cityError: provider.getFieldError('city'),
                    districtError: provider.getFieldError('district'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 夏令时选项
                  CustomCheckbox(
                    label: '夏令时',
                    value: formData.isDaylightSaving,
                    onChanged: (value) {
                      provider.updateFormData(formData.copyWith(isDaylightSaving: value));
                    },
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 真太阳时说明
                  _buildSolarTimeNote(context),
                ],
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 子时规则区域
            FormSection(
              title: '子时规则',
              icon: Icons.schedule_outlined,
              children: [
                ZiTimeRuleSelector(
                  value: formData.ziTimeRule,
                  onChanged: (value) {
                    provider.updateFormData(formData.copyWith(ziTimeRule: value));
                  },
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// 构建日期时间输入组件
  Widget _buildDateTimeInputs(BuildContext context, ChartProvider provider, formData) {
    final isMobile = ResponsiveUtils.isMobile(context);
    
    if (isMobile) {
      // 移动端垂直布局
      return Column(
        children: [
          CustomTextField(
            label: '出生年份',
            value: formData.birthYear?.toString() ?? '',
            onChanged: (value) {
              final year = int.tryParse(value);
              provider.updateFormData(formData.copyWith(birthYear: year));
            },
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            errorText: provider.getFieldError('birthYear'),
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: '出生月份',
            value: formData.birthMonth?.toString() ?? '',
            onChanged: (value) {
              final month = int.tryParse(value);
              provider.updateFormData(formData.copyWith(birthMonth: month));
            },
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            errorText: provider.getFieldError('birthMonth'),
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: '出生日期',
            value: formData.birthDay?.toString() ?? '',
            onChanged: (value) {
              final day = int.tryParse(value);
              provider.updateFormData(formData.copyWith(birthDay: day));
            },
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            errorText: provider.getFieldError('birthDay'),
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomTextField(
            label: '出生时辰',
            value: formData.birthHour?.toString() ?? '',
            onChanged: (value) {
              final hour = int.tryParse(value);
              provider.updateFormData(formData.copyWith(birthHour: hour));
            },
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            errorText: provider.getFieldError('birthHour'),
            isRequired: true,
          ),
        ],
      );
    } else {
      // 桌面端水平布局
      return Row(
        children: [
          Expanded(
            child: CustomTextField(
              label: '出生年份',
              value: formData.birthYear?.toString() ?? '',
              onChanged: (value) {
                final year = int.tryParse(value);
                provider.updateFormData(formData.copyWith(birthYear: year));
              },
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              errorText: provider.getFieldError('birthYear'),
              isRequired: true,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomTextField(
              label: '出生月份',
              value: formData.birthMonth?.toString() ?? '',
              onChanged: (value) {
                final month = int.tryParse(value);
                provider.updateFormData(formData.copyWith(birthMonth: month));
              },
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              errorText: provider.getFieldError('birthMonth'),
              isRequired: true,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomTextField(
              label: '出生日期',
              value: formData.birthDay?.toString() ?? '',
              onChanged: (value) {
                final day = int.tryParse(value);
                provider.updateFormData(formData.copyWith(birthDay: day));
              },
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              errorText: provider.getFieldError('birthDay'),
              isRequired: true,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomTextField(
              label: '出生时辰',
              value: formData.birthHour?.toString() ?? '',
              onChanged: (value) {
                final hour = int.tryParse(value);
                provider.updateFormData(formData.copyWith(birthHour: hour));
              },
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              errorText: provider.getFieldError('birthHour'),
              isRequired: true,
            ),
          ),
        ],
      );
    }
  }

  /// 构建真太阳时说明
  Widget _buildSolarTimeNote(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '真太阳时校准说明：根据出生地经纬度计算真太阳时，提高排盘准确性',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
