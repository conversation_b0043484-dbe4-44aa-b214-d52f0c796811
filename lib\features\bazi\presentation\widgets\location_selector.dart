import 'package:flutter/material.dart';
import '../../../../shared/utils/responsive_utils.dart';
import 'custom_dropdown.dart';

/// 地址选择模式
enum LocationMode {
  domestic, // 国内模式：省市县三级联动
  international, // 国际模式：国家城市二级联动
}

/// 地址选择组件
class LocationSelector extends StatelessWidget {
  final LocationMode mode;
  final String? province;
  final String? city;
  final String? district;
  final String? country;
  final ValueChanged<String?>? onProvinceChanged;
  final ValueChanged<String?>? onCityChanged;
  final ValueChanged<String?>? onDistrictChanged;
  final ValueChanged<String?>? onCountryChanged;
  final String? provinceError;
  final String? cityError;
  final String? districtError;
  final String? countryError;

  const LocationSelector({
    super.key,
    required this.mode,
    this.province,
    this.city,
    this.district,
    this.country,
    this.onProvinceChanged,
    this.onCityChanged,
    this.onDistrictChanged,
    this.onCountryChanged,
    this.provinceError,
    this.cityError,
    this.districtError,
    this.countryError,
  });

  @override
  Widget build(BuildContext context) {
    if (mode == LocationMode.domestic) {
      return _buildDomesticSelector(context);
    } else {
      return _buildInternationalSelector(context);
    }
  }

  /// 构建国内地址选择器
  Widget _buildDomesticSelector(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);
    
    if (isMobile) {
      // 移动端垂直布局
      return Column(
        children: [
          CustomDropdown<String>(
            label: '省份',
            value: province,
            items: _getProvinces(),
            onChanged: onProvinceChanged,
            errorText: provinceError,
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown<String>(
            label: '城市',
            value: city,
            items: _getCities(province),
            onChanged: onCityChanged,
            errorText: cityError,
            isRequired: true,
            enabled: province != null,
          ),
          const SizedBox(height: 16),
          CustomDropdown<String>(
            label: '县区',
            value: district,
            items: _getDistricts(province, city),
            onChanged: onDistrictChanged,
            errorText: districtError,
            isRequired: true,
            enabled: city != null,
          ),
        ],
      );
    } else {
      // 桌面端水平布局
      return Row(
        children: [
          Expanded(
            child: CustomDropdown<String>(
              label: '省份',
              value: province,
              items: _getProvinces(),
              onChanged: onProvinceChanged,
              errorText: provinceError,
              isRequired: true,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomDropdown<String>(
              label: '城市',
              value: city,
              items: _getCities(province),
              onChanged: onCityChanged,
              errorText: cityError,
              isRequired: true,
              enabled: province != null,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomDropdown<String>(
              label: '县区',
              value: district,
              items: _getDistricts(province, city),
              onChanged: onDistrictChanged,
              errorText: districtError,
              isRequired: true,
              enabled: city != null,
            ),
          ),
        ],
      );
    }
  }

  /// 构建国际地址选择器
  Widget _buildInternationalSelector(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);
    
    if (isMobile) {
      // 移动端垂直布局
      return Column(
        children: [
          CustomDropdown<String>(
            label: '国家',
            value: country,
            items: _getCountries(),
            onChanged: onCountryChanged,
            errorText: countryError,
            isRequired: true,
          ),
          const SizedBox(height: 16),
          CustomDropdown<String>(
            label: '城市',
            value: city,
            items: _getInternationalCities(country),
            onChanged: onCityChanged,
            errorText: cityError,
            isRequired: true,
            enabled: country != null,
          ),
        ],
      );
    } else {
      // 桌面端水平布局
      return Row(
        children: [
          Expanded(
            child: CustomDropdown<String>(
              label: '国家',
              value: country,
              items: _getCountries(),
              onChanged: onCountryChanged,
              errorText: countryError,
              isRequired: true,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: CustomDropdown<String>(
              label: '城市',
              value: city,
              items: _getInternationalCities(country),
              onChanged: onCityChanged,
              errorText: cityError,
              isRequired: true,
              enabled: country != null,
            ),
          ),
        ],
      );
    }
  }

  /// 获取省份列表（模拟数据）
  List<String> _getProvinces() {
    return [
      '北京市',
      '天津市',
      '河北省',
      '山西省',
      '内蒙古自治区',
      '辽宁省',
      '吉林省',
      '黑龙江省',
      '上海市',
      '江苏省',
      '浙江省',
      '安徽省',
      '福建省',
      '江西省',
      '山东省',
      '河南省',
      '湖北省',
      '湖南省',
      '广东省',
      '广西壮族自治区',
      '海南省',
      '重庆市',
      '四川省',
      '贵州省',
      '云南省',
      '西藏自治区',
      '陕西省',
      '甘肃省',
      '青海省',
      '宁夏回族自治区',
      '新疆维吾尔自治区',
      '台湾省',
      '香港特别行政区',
      '澳门特别行政区',
    ];
  }

  /// 获取城市列表（模拟数据）
  List<String> _getCities(String? province) {
    if (province == null) return [];
    
    // 这里应该根据省份返回对应的城市列表
    // 为了演示，返回一些示例城市
    switch (province) {
      case '北京市':
        return ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'];
      case '上海市':
        return ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'];
      case '广东省':
        return ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'];
      default:
        return ['市辖区', '县级市', '县'];
    }
  }

  /// 获取县区列表（模拟数据）
  List<String> _getDistricts(String? province, String? city) {
    if (province == null || city == null) return [];
    
    // 这里应该根据省份和城市返回对应的县区列表
    // 为了演示，返回一些示例县区
    return ['区县1', '区县2', '区县3', '区县4', '区县5'];
  }

  /// 获取国家列表（模拟数据）
  List<String> _getCountries() {
    return [
      '中国',
      '美国',
      '英国',
      '法国',
      '德国',
      '日本',
      '韩国',
      '新加坡',
      '澳大利亚',
      '加拿大',
      '俄罗斯',
      '印度',
      '巴西',
      '意大利',
      '西班牙',
      '荷兰',
      '瑞士',
      '瑞典',
      '挪威',
      '丹麦',
    ];
  }

  /// 获取国际城市列表（模拟数据）
  List<String> _getInternationalCities(String? country) {
    if (country == null) return [];
    
    // 这里应该根据国家返回对应的城市列表
    // 为了演示，返回一些示例城市
    switch (country) {
      case '美国':
        return ['纽约', '洛杉矶', '芝加哥', '休斯顿', '费城', '凤凰城', '圣安东尼奥', '圣地亚哥', '达拉斯', '圣何塞'];
      case '英国':
        return ['伦敦', '伯明翰', '利兹', '格拉斯哥', '谢菲尔德', '布拉德福德', '爱丁堡', '利物浦', '曼彻斯特'];
      case '日本':
        return ['东京', '大阪', '名古屋', '札幌', '神户', '京都', '福冈', '川崎', '埼玉', '广岛'];
      default:
        return ['首都', '主要城市1', '主要城市2', '主要城市3'];
    }
  }
}
