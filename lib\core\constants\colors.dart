import 'package:flutter/material.dart';

/// 颜色常量定义 - 融合传统文化的八字命理色彩系统
class AppColors {
  AppColors._();

  // === 传统文化主色系 ===

  // 日间主题颜色 - 以传统文化为灵感
  static const Color lightPrimaryText = Color(0xFF1C1C1C); // 深墨色 - 更深邃的文字色
  static const Color lightSecondaryText = Color(0xFF4A4A4A); // 淡墨色 - 次级文字
  static const Color lightTertiaryText = Color(0xFF6B6B6B); // 浅墨色 - 辅助文字
  static const Color lightAccentColor = Color(0xFF1B365D); // 深墨蓝 - 主色调，象征智慧深邃
  static const Color lightSecondaryColor = Color(0xFFD4A574); // 琥珀金 - 辅助色，象征传统典雅
  static const Color lightBackgroundColor = Color(0xFFFEFCF8); // 温润白 - 如宣纸般温和
  static const Color lightSurfaceColor = Color(0xFFFFFFFE); // 纯净白 - 卡片背景
  static const Color lightCardColor = Color(0xFFFDFBF7); // 微暖白 - 卡片色彩
  static const Color lightBorderColor = Color(0xFFE8E3DB); // 淡雅边框 - 与背景协调
  static const Color lightSoftBorderColor = Color(0xFFF0EDE6); // 柔和边框 - 更淡的分割线
  static const Color lightDividerColor = Color(0xFFEFECE5); // 分割线色

  // 夜间主题颜色 - 保持文化韵味的深色系
  static const Color darkPrimaryText = Color(0xFFE8E6E3); // 温润白文字
  static const Color darkSecondaryText = Color(0xFFB8B5B0); // 次要文字
  static const Color darkTertiaryText = Color(0xFF8B8883); // 辅助文字
  static const Color darkAccentColor = Color(0xFF4A7BA7); // 明亮墨蓝 - 夜间主色
  static const Color darkSecondaryColor = Color(0xFFE6C794); // 明亮琥珀金 - 夜间辅助色
  static const Color darkBackgroundColor = Color(0xFF1A1A1A); // 深邃背景
  static const Color darkSurfaceColor = Color(0xFF2A2A2A); // 卡片表面
  static const Color darkCardColor = Color(0xFF252525); // 卡片背景
  static const Color darkBorderColor = Color(0xFF3A3A3A); // 边框颜色
  static const Color darkSoftBorderColor = Color(0xFF333333); // 柔和边框
  static const Color darkDividerColor = Color(0xFF2E2E2E); // 分割线色

  // === 功能色彩系统 ===

  // 状态色彩 - 融入传统文化元素
  static const Color successColor = Color(0xFF2E7D32); // 深绿 - 象征生机
  static const Color warningColor = Color(0xFFE65100); // 橙红 - 象征警示
  static const Color errorColor = Color(0xFFC73E1D); // 朱砂红 - 传统警示色
  static const Color infoColor = Color(0xFF1565C0); // 深蓝 - 信息提示

  // 五行色彩系统 - 体现八字文化
  static const Color wuxingWood = Color(0xFF2E7D32); // 木 - 深绿色
  static const Color wuxingFire = Color(0xFFC73E1D); // 火 - 朱砂红
  static const Color wuxingEarth = Color(0xFFD4A574); // 土 - 琥珀金
  static const Color wuxingMetal = Color(0xFF78909C); // 金 - 银灰色
  static const Color wuxingWater = Color(0xFF1B365D); // 水 - 深墨蓝

  // Agent 颜色 - 重新设计更符合文化内涵
  static const Color agentBaziMaster = Color(0xFF1B365D); // 八字大师 - 深墨蓝
  static const Color agentCareer = Color(0xFFD4A574); // 事业运势 - 琥珀金
  static const Color agentEmotion = Color(0xFFC73E1D); // 情感分析 - 朱砂红
  static const Color agentHealth = Color(0xFF2E7D32); // 健康运势 - 深绿色

  // === 渐变色彩定义 ===

  // 主色调渐变
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF1B365D), Color(0xFF2E5984)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 辅助色渐变
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFFD4A574), Color(0xFFE6C794)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 卡片阴影渐变
  static const LinearGradient cardShadowGradient = LinearGradient(
    colors: [Color(0x08000000), Color(0x00000000)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // === 透明度系统 ===

  // 基础透明度
  static const double opacityDisabled = 0.38; // 禁用状态
  static const double opacityMedium = 0.54; // 中等透明度
  static const double opacityHigh = 0.87; // 高透明度

  // 交互透明度
  static const double opacityHover = 0.04; // 悬停效果
  static const double opacityPressed = 0.12; // 按压效果
  static const double opacityFocus = 0.12; // 焦点效果
  static const double opacitySelected = 0.08; // 选中效果

  // 背景透明度
  static const double opacityOverlay = 0.16; // 遮罩层
  static const double opacityBackdrop = 0.54; // 背景遮罩
}