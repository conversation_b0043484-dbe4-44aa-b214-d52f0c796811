import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// 响应式容器组件
/// 根据屏幕尺寸自动调整内容布局和边距
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;
  final bool centerContent;
  final Color? backgroundColor;
  final BoxDecoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
    this.centerContent = true,
    this.backgroundColor,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final screenType = ResponsiveUtils.getScreenType(context);
    final contentMaxWidth = maxWidth ?? ResponsiveUtils.getContentMaxWidth(context);
    final responsivePadding = padding ?? _getDefaultPadding(screenType);
    final responsiveMargin = margin ?? _getDefaultMargin(screenType);

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: contentMaxWidth),
      padding: responsivePadding,
      margin: responsiveMargin,
      decoration: decoration ?? (backgroundColor != null 
          ? BoxDecoration(color: backgroundColor) 
          : null),
      child: child,
    );

    if (centerContent && screenType != ScreenType.mobile) {
      content = Center(child: content);
    }

    return content;
  }

  /// 获取默认内边距 - 优化版本
  EdgeInsets _getDefaultPadding(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
      case ScreenType.tablet:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 16);
      case ScreenType.desktop:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 20);
      case ScreenType.largeDesktop:
        return const EdgeInsets.symmetric(horizontal: 40, vertical: 24);
    }
  }

  /// 获取默认外边距
  EdgeInsets _getDefaultMargin(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return EdgeInsets.zero;
      case ScreenType.tablet:
        return const EdgeInsets.symmetric(horizontal: 16);
      case ScreenType.desktop:
        return const EdgeInsets.symmetric(horizontal: 24);
      case ScreenType.largeDesktop:
        return const EdgeInsets.symmetric(horizontal: 32);
    }
  }
}

/// 响应式卡片容器
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final String? title;
  final Widget? titleWidget;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? elevation;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.title,
    this.titleWidget,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenType = ResponsiveUtils.getScreenType(context);
    
    final cardPadding = padding ?? _getCardPadding(screenType);
    final cardMargin = margin ?? _getCardMargin(screenType);
    final cardElevation = elevation ?? _getCardElevation(screenType);

    return Container(
      margin: cardMargin,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.12),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.08),
            offset: Offset(0, cardElevation * 0.5),
            blurRadius: cardElevation * 3,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.04),
            offset: Offset(0, cardElevation * 0.25),
            blurRadius: cardElevation,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题区域
            if (title != null || titleWidget != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.fromLTRB(
                  cardPadding.left,
                  cardPadding.top,
                  cardPadding.right,
                  16,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.03),
                  border: Border(
                    bottom: BorderSide(
                      color: theme.colorScheme.outline.withOpacity(0.08),
                      width: 1,
                    ),
                  ),
                ),
                child: titleWidget ?? Text(
                  title!,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),

              // 内容区域
              Padding(
                padding: EdgeInsets.fromLTRB(
                  cardPadding.left,
                  16,
                  cardPadding.right,
                  cardPadding.bottom,
                ),
                child: child,
              ),
            ] else ...[
              // 无标题时的内容区域
              Padding(
                padding: cardPadding,
                child: child,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 获取卡片内边距 - 优化版本
  EdgeInsets _getCardPadding(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return const EdgeInsets.all(16);
      case ScreenType.tablet:
        return const EdgeInsets.all(20);
      case ScreenType.desktop:
        return const EdgeInsets.all(24);
      case ScreenType.largeDesktop:
        return const EdgeInsets.all(28);
    }
  }

  /// 获取卡片外边距 - 优化版本
  EdgeInsets _getCardMargin(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case ScreenType.tablet:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ScreenType.desktop:
        return const EdgeInsets.symmetric(horizontal: 20, vertical: 10);
      case ScreenType.largeDesktop:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    }
  }

  /// 获取卡片阴影
  double _getCardElevation(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return 1;
      case ScreenType.tablet:
        return 2;
      case ScreenType.desktop:
        return 3;
      case ScreenType.largeDesktop:
        return 4;
    }
  }
}

/// 响应式网格布局
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final int? largeDesktopColumns;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.largeDesktopColumns,
    this.spacing = 16,
    this.runSpacing = 16,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final screenType = ResponsiveUtils.getScreenType(context);
    final columns = _getColumns(screenType);
    
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Wrap(
        spacing: spacing,
        runSpacing: runSpacing,
        children: children.map((child) {
          return SizedBox(
            width: (MediaQuery.of(context).size.width - 
                   (padding?.horizontal ?? 0) - 
                   (spacing * (columns - 1))) / columns,
            child: child,
          );
        }).toList(),
      ),
    );
  }

  /// 获取列数
  int _getColumns(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return mobileColumns ?? 1;
      case ScreenType.tablet:
        return tabletColumns ?? 2;
      case ScreenType.desktop:
        return desktopColumns ?? 3;
      case ScreenType.largeDesktop:
        return largeDesktopColumns ?? 4;
    }
  }
}

/// 响应式间距组件
class ResponsiveSpacing extends StatelessWidget {
  final double? mobile;
  final double? tablet;
  final double? desktop;
  final double? largeDesktop;
  final bool isVertical;

  const ResponsiveSpacing({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
    this.isVertical = true,
  });

  const ResponsiveSpacing.horizontal({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  }) : isVertical = false;

  @override
  Widget build(BuildContext context) {
    final spacing = ResponsiveUtils.responsiveValue(
      context,
      mobile: mobile ?? (isVertical ? 16 : 12),
      tablet: tablet ?? (isVertical ? 20 : 16),
      desktop: desktop ?? (isVertical ? 24 : 20),
      largeDesktop: largeDesktop ?? (isVertical ? 32 : 24),
    );

    return SizedBox(
      width: isVertical ? null : spacing,
      height: isVertical ? spacing : null,
    );
  }
}
