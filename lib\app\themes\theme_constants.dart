import 'package:flutter/material.dart';
import '../../core/constants/colors.dart';

/// 主题常量定义
class ThemeConstants {
  ThemeConstants._();

  // === 字体系统 - 针对中文优化 ===

  // 字体大小层级 - 基于1.25倍比例设计
  static const double fontSizeCaption = 10.0;    // 说明文字
  static const double fontSizeSmall = 12.0;      // 小字
  static const double fontSizeBody = 14.0;       // 正文
  static const double fontSizeBodyLarge = 16.0;  // 大正文
  static const double fontSizeSubtitle = 18.0;   // 副标题
  static const double fontSizeTitle = 20.0;      // 标题
  static const double fontSizeTitleLarge = 24.0; // 大标题
  static const double fontSizeHeadline = 28.0;   // 页面标题
  static const double fontSizeDisplay = 32.0;    // 展示标题
  static const double fontSizeDisplayLarge = 40.0; // 大展示标题

  // 字体权重 - 针对中文显示优化
  static const FontWeight fontWeightThin = FontWeight.w200;     // 极细
  static const FontWeight fontWeightLight = FontWeight.w300;    // 细体
  static const FontWeight fontWeightRegular = FontWeight.w400;  // 常规
  static const FontWeight fontWeightMedium = FontWeight.w500;   // 中等
  static const FontWeight fontWeightSemiBold = FontWeight.w600; // 半粗
  static const FontWeight fontWeightBold = FontWeight.w700;     // 粗体
  static const FontWeight fontWeightExtraBold = FontWeight.w800; // 特粗

  // 行高系统 - 针对中文阅读优化
  static const double lineHeightTight = 1.2;    // 紧凑行高
  static const double lineHeightNormal = 1.4;   // 标准行高
  static const double lineHeightRelaxed = 1.6;  // 宽松行高
  static const double lineHeightLoose = 1.8;    // 疏松行高

  // 字间距系统
  static const double letterSpacingTight = -0.5;  // 紧凑字间距
  static const double letterSpacingNormal = 0.0;  // 标准字间距
  static const double letterSpacingWide = 0.5;    // 宽松字间距
  static const double letterSpacingExtraWide = 1.0; // 超宽字间距

  // === 阴影系统 - 层次化设计 ===

  // 卡片阴影 - 不同层级
  static const List<BoxShadow> lightCardShadowLow = [
    BoxShadow(
      color: Color(0x06000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> lightCardShadowMedium = [
    BoxShadow(
      color: Color(0x08000000),
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x04000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> lightCardShadowHigh = [
    BoxShadow(
      color: Color(0x0A000000),
      offset: Offset(0, 4),
      blurRadius: 16,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x06000000),
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // 暗色主题阴影
  static const List<BoxShadow> darkCardShadowLow = [
    BoxShadow(
      color: Color(0x20000000),
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> darkCardShadowMedium = [
    BoxShadow(
      color: Color(0x30000000),
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static const List<BoxShadow> darkCardShadowHigh = [
    BoxShadow(
      color: Color(0x40000000),
      offset: Offset(0, 4),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];

  // 按钮阴影
  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x20000000),
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // 浮动阴影（用于悬浮元素）
  static const List<BoxShadow> floatingShadow = [
    BoxShadow(
      color: Color(0x15000000),
      offset: Offset(0, 8),
      blurRadius: 24,
      spreadRadius: 0,
    ),
  ];

  // === 颜色方案 - 使用优化的色彩系统 ===

  // 亮色主题颜色方案
  static const ColorScheme lightColorScheme = ColorScheme.light(
    primary: AppColors.lightAccentColor,           // 深墨蓝主色
    onPrimary: Colors.white,                       // 主色上的文字
    secondary: AppColors.lightSecondaryColor,      // 琥珀金辅助色
    onSecondary: AppColors.lightPrimaryText,       // 辅助色上的文字
    tertiary: AppColors.wuxingWood,               // 第三色彩
    onTertiary: Colors.white,                      // 第三色彩上的文字
    surface: AppColors.lightSurfaceColor,          // 表面色
    onSurface: AppColors.lightPrimaryText,         // 表面上的文字
    surfaceVariant: AppColors.lightCardColor,      // 表面变体色
    onSurfaceVariant: AppColors.lightSecondaryText, // 表面变体上的文字
    background: AppColors.lightBackgroundColor,    // 背景色
    onBackground: AppColors.lightPrimaryText,      // 背景上的文字
    error: AppColors.errorColor,                   // 错误色
    onError: Colors.white,                         // 错误色上的文字
    outline: AppColors.lightBorderColor,           // 轮廓色
    outlineVariant: AppColors.lightSoftBorderColor, // 轮廓变体色
  );

  // 暗色主题颜色方案
  static const ColorScheme darkColorScheme = ColorScheme.dark(
    primary: AppColors.darkAccentColor,            // 明亮墨蓝主色
    onPrimary: Colors.white,                       // 主色上的文字
    secondary: AppColors.darkSecondaryColor,       // 明亮琥珀金辅助色
    onSecondary: AppColors.darkPrimaryText,        // 辅助色上的文字
    tertiary: AppColors.wuxingWood,               // 第三色彩
    onTertiary: Colors.white,                      // 第三色彩上的文字
    surface: AppColors.darkSurfaceColor,           // 表面色
    onSurface: AppColors.darkPrimaryText,          // 表面上的文字
    surfaceVariant: AppColors.darkCardColor,       // 表面变体色
    onSurfaceVariant: AppColors.darkSecondaryText, // 表面变体上的文字
    background: AppColors.darkBackgroundColor,     // 背景色
    onBackground: AppColors.darkPrimaryText,       // 背景上的文字
    error: AppColors.errorColor,                   // 错误色
    onError: Colors.white,                         // 错误色上的文字
    outline: AppColors.darkBorderColor,            // 轮廓色
    outlineVariant: AppColors.darkSoftBorderColor, // 轮廓变体色
  );

  // === 边框样式系统 ===

  // 基础边框样式
  static const BorderSide lightBorderSide = BorderSide(
    color: AppColors.lightBorderColor,
    width: 1.0,
  );

  static const BorderSide darkBorderSide = BorderSide(
    color: AppColors.darkBorderColor,
    width: 1.0,
  );

  // 柔和边框样式（用于分割线等）
  static const BorderSide lightSoftBorderSide = BorderSide(
    color: AppColors.lightSoftBorderColor,
    width: 1.0,
  );

  static const BorderSide darkSoftBorderSide = BorderSide(
    color: AppColors.darkSoftBorderColor,
    width: 1.0,
  );

  // 焦点边框样式
  static const BorderSide lightFocusedBorderSide = BorderSide(
    color: AppColors.lightAccentColor,
    width: 2.0,
  );

  static const BorderSide darkFocusedBorderSide = BorderSide(
    color: AppColors.darkAccentColor,
    width: 2.0,
  );

  // 错误边框样式
  static const BorderSide errorBorderSide = BorderSide(
    color: AppColors.errorColor,
    width: 1.5,
  );

  // 成功边框样式
  static const BorderSide successBorderSide = BorderSide(
    color: AppColors.successColor,
    width: 1.5,
  );

  // === 圆角样式系统 ===

  // 基础圆角
  static const BorderRadius borderRadiusNone = BorderRadius.zero;
  static const BorderRadius borderRadiusXS = BorderRadius.all(Radius.circular(2));
  static const BorderRadius borderRadiusS = BorderRadius.all(Radius.circular(4));
  static const BorderRadius borderRadiusM = BorderRadius.all(Radius.circular(8));
  static const BorderRadius borderRadiusL = BorderRadius.all(Radius.circular(12));
  static const BorderRadius borderRadiusXL = BorderRadius.all(Radius.circular(16));
  static const BorderRadius borderRadiusXXL = BorderRadius.all(Radius.circular(20));
  static const BorderRadius borderRadiusRound = BorderRadius.all(Radius.circular(50));

  // 特殊圆角（用于特定组件）
  static const BorderRadius cardBorderRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius buttonBorderRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius inputBorderRadius = BorderRadius.all(Radius.circular(8));
  static const BorderRadius dialogBorderRadius = BorderRadius.all(Radius.circular(16));
  static const BorderRadius bottomSheetBorderRadius = BorderRadius.only(
    topLeft: Radius.circular(20),
    topRight: Radius.circular(20),
  );

  // === 动画系统 ===

  // 动画曲线
  static const Curve easeInOutCurve = Curves.easeInOut;
  static const Curve easeOutCurve = Curves.easeOut;
  static const Curve easeInCurve = Curves.easeIn;
  static const Curve fastOutSlowInCurve = Curves.fastOutSlowIn;
  static const Curve bounceInCurve = Curves.bounceIn;
  static const Curve bounceOutCurve = Curves.bounceOut;
  static const Curve elasticInCurve = Curves.elasticIn;
  static const Curve elasticOutCurve = Curves.elasticOut;

  // 动画持续时间
  static const Duration animationDurationFast = Duration(milliseconds: 150);
  static const Duration animationDurationNormal = Duration(milliseconds: 250);
  static const Duration animationDurationSlow = Duration(milliseconds: 350);
  static const Duration animationDurationVerySlow = Duration(milliseconds: 500);

  // 页面转场动画持续时间
  static const Duration pageTransitionDuration = Duration(milliseconds: 300);
  static const Duration dialogTransitionDuration = Duration(milliseconds: 250);
  static const Duration bottomSheetTransitionDuration = Duration(milliseconds: 300);
}