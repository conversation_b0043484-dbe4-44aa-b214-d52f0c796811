import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_container.dart';
import '../providers/chart_provider.dart';
import 'chart_mode_tabs.dart';
import 'bazi_form_content.dart';

/// 八字排盘主要内容组件
class BaziChartContent extends StatelessWidget {
  const BaziChartContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 排盘模式选择标签页（国内排盘/国际排盘/直接排盘）
        ResponsiveCard(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: const ChartModeTabs(),
        ),

        // 表单内容区域
        Expanded(
          child: Consumer<ChartProvider>(
            builder: (context, provider, child) {
              return SingleChildScrollView(
                child: ResponsiveContainer(
                  child: const BaziFormContent(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
