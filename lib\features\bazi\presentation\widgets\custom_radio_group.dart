import 'package:flutter/material.dart';
import '../../../../shared/utils/responsive_utils.dart';

/// 自定义单选按钮组组件
class CustomRadioGroup<T> extends StatelessWidget {
  final String label;
  final T? value;
  final List<T> options;
  final List<String> optionLabels;
  final ValueChanged<T?> onChanged;
  final String? errorText;
  final bool isRequired;
  final bool enabled;
  final Axis direction;

  const CustomRadioGroup({
    super.key,
    required this.label,
    required this.value,
    required this.options,
    required this.optionLabels,
    required this.onChanged,
    this.errorText,
    this.isRequired = false,
    this.enabled = true,
    this.direction = Axis.horizontal,
  }) : assert(options.length == optionLabels.length);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final hasError = errorText != null && errorText!.isNotEmpty;
    final isMobile = ResponsiveUtils.isMobile(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        _buildLabel(theme),
        
        const SizedBox(height: 8),
        
        // 单选按钮组 - 优化版本
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: hasError
                  ? theme.colorScheme.error.withOpacity(0.5)
                  : theme.colorScheme.outline.withOpacity(0.2),
              width: hasError ? 1.5 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
            color: enabled
                ? theme.colorScheme.surface
                : theme.colorScheme.surface.withOpacity(0.5),
            boxShadow: hasError ? [] : [
              BoxShadow(
                color: theme.colorScheme.shadow.withOpacity(0.04),
                offset: const Offset(0, 1),
                blurRadius: 3,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: _buildRadioOptions(context, theme, isMobile),
          ),
        ),
        
        // 错误信息
        if (hasError) ...[
          const SizedBox(height: 4),
          _buildErrorText(theme),
        ],
      ],
    );
  }

  /// 构建标签
  Widget _buildLabel(ThemeData theme) {
    return RichText(
      text: TextSpan(
        style: theme.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: theme.colorScheme.onSurface,
        ),
        children: [
          TextSpan(text: label),
          if (isRequired)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建单选按钮选项
  Widget _buildRadioOptions(BuildContext context, ThemeData theme, bool isMobile) {
    final widgets = options.asMap().entries.map((entry) {
      final index = entry.key;
      final option = entry.value;
      final optionLabel = optionLabels[index];
      
      return _RadioOption<T>(
        value: option,
        groupValue: value,
        label: optionLabel,
        onChanged: enabled ? onChanged : null,
        theme: theme,
      );
    }).toList();

    // 根据方向和屏幕大小决定布局
    if (direction == Axis.horizontal && !isMobile && widgets.length <= 3) {
      return Row(
        children: widgets.map((widget) => Expanded(child: widget)).toList(),
      );
    } else {
      return Column(
        children: widgets,
      );
    }
  }

  /// 构建错误信息
  Widget _buildErrorText(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.error_outline,
          size: 14,
          color: theme.colorScheme.error,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ),
      ],
    );
  }
}

/// 单个单选按钮选项
class _RadioOption<T> extends StatelessWidget {
  final T value;
  final T? groupValue;
  final String label;
  final ValueChanged<T?>? onChanged;
  final ThemeData theme;

  const _RadioOption({
    required this.value,
    required this.groupValue,
    required this.label,
    required this.onChanged,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = value == groupValue;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: isSelected
            ? theme.colorScheme.primary.withOpacity(0.08)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(
                color: theme.colorScheme.primary.withOpacity(0.3),
                width: 1,
              )
            : null,
      ),
      child: InkWell(
        onTap: onChanged != null ? () => onChanged!(value) : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Radio<T>(
                value: value,
                groupValue: groupValue,
                onChanged: onChanged,
                activeColor: theme.colorScheme.primary,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: onChanged != null
                        ? (isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface)
                        : theme.colorScheme.onSurface.withOpacity(0.5),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
