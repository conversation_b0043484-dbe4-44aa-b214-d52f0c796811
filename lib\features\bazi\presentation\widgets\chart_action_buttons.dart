import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';

/// 排盘操作按钮组件
class ChartActionButtons extends StatelessWidget {
  const ChartActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        final isMobile = ResponsiveUtils.isMobile(context);
        
        if (isMobile) {
          // 移动端垂直布局
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildSubmitButton(context, provider),
              const SizedBox(height: 12),
              _buildResetButton(context, provider),
            ],
          );
        } else {
          // 桌面端水平布局
          return Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildSubmitButton(context, provider),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildResetButton(context, provider),
              ),
            ],
          );
        }
      },
    );
  }

  /// 构建提交按钮
  Widget _buildSubmitButton(BuildContext context, ChartProvider provider) {
    final theme = Theme.of(context);
    final isSubmitting = provider.isSubmitting;
    final canSubmit = _canSubmit(provider);
    
    return ElevatedButton(
      onPressed: canSubmit && !isSubmitting ? () => _handleSubmit(context, provider) : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        elevation: 0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isSubmitting) ...[
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.onPrimary,
                ),
              ),
            ),
            const SizedBox(width: 12),
          ] else ...[
            Icon(
              Icons.calculate_outlined,
              size: 18,
              color: theme.colorScheme.onPrimary,
            ),
            const SizedBox(width: 8),
          ],
          Text(
            isSubmitting ? '正在排盘...' : '开始排盘',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建重置按钮
  Widget _buildResetButton(BuildContext context, ChartProvider provider) {
    final theme = Theme.of(context);
    final isSubmitting = provider.isSubmitting;
    
    return OutlinedButton(
      onPressed: !isSubmitting ? () => _handleReset(context, provider) : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: theme.colorScheme.onSurface,
        side: BorderSide(color: theme.dividerColor),
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.refresh_outlined,
            size: 18,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          const SizedBox(width: 8),
          Text(
            '重置',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 检查是否可以提交
  bool _canSubmit(ChartProvider provider) {
    if (provider.currentChartMode == ChartMode.direct) {
      return provider.directChartData.isValid;
    } else {
      return provider.formData.isValid;
    }
  }

  /// 处理提交操作
  Future<void> _handleSubmit(BuildContext context, ChartProvider provider) async {
    try {
      await provider.submitChart();
      
      if (context.mounted) {
        _showSuccessMessage(context);
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, e.toString());
      }
    }
  }

  /// 处理重置操作
  void _handleReset(BuildContext context, ChartProvider provider) {
    showDialog(
      context: context,
      builder: (context) => _ResetConfirmDialog(
        onConfirm: () {
          provider.resetForm();
          Navigator.of(context).pop();
          _showResetMessage(context);
        },
      ),
    );
  }

  /// 显示成功消息
  void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text('排盘成功！'),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text('排盘失败：$message')),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示重置消息
  void _showResetMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.refresh, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text('表单已重置'),
          ],
        ),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// 重置确认对话框
class _ResetConfirmDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const _ResetConfirmDialog({required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_outlined,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text('确认重置'),
        ],
      ),
      content: const Text('确定要重置所有输入的信息吗？此操作不可撤销。'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            '取消',
            style: TextStyle(color: theme.colorScheme.onSurface.withOpacity(0.6)),
          ),
        ),
        ElevatedButton(
          onPressed: onConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
          ),
          child: const Text('确认重置'),
        ),
      ],
    );
  }
}
