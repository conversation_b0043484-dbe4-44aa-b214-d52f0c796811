import 'chart_types.dart';

/// 排盘表单数据模型
class ChartFormData {
  // 基础信息
  String name;
  Gender? gender;
  CalendarType calendarType;
  
  // 出生日期时间
  int? birthYear;
  int? birthMonth;
  int? birthDay;
  int? birthHour;
  
  // 农历特殊选项
  bool isLeapMonth;
  
  // 地址相关
  bool enableLocationInput;
  String? province;
  String? city;
  String? district;
  String? country;
  bool isDaylightSaving;
  
  // 子时规则
  ZiTimeRule ziTimeRule;

  ChartFormData({
    this.name = '',
    this.gender,
    this.calendarType = CalendarType.solar,
    this.birthYear,
    this.birthMonth,
    this.birthDay,
    this.birthHour,
    this.isLeapMonth = false,
    this.enableLocationInput = false,
    this.province,
    this.city,
    this.district,
    this.country,
    this.isDaylightSaving = false,
    this.ziTimeRule = ZiTimeRule.modern,
  });

  /// 复制并修改数据
  ChartFormData copyWith({
    String? name,
    Gender? gender,
    CalendarType? calendarType,
    int? birthYear,
    int? birthMonth,
    int? birthDay,
    int? birthHour,
    bool? isLeapMonth,
    bool? enableLocationInput,
    String? province,
    String? city,
    String? district,
    String? country,
    bool? isDaylightSaving,
    ZiTimeRule? ziTimeRule,
  }) {
    return ChartFormData(
      name: name ?? this.name,
      gender: gender ?? this.gender,
      calendarType: calendarType ?? this.calendarType,
      birthYear: birthYear ?? this.birthYear,
      birthMonth: birthMonth ?? this.birthMonth,
      birthDay: birthDay ?? this.birthDay,
      birthHour: birthHour ?? this.birthHour,
      isLeapMonth: isLeapMonth ?? this.isLeapMonth,
      enableLocationInput: enableLocationInput ?? this.enableLocationInput,
      province: province ?? this.province,
      city: city ?? this.city,
      district: district ?? this.district,
      country: country ?? this.country,
      isDaylightSaving: isDaylightSaving ?? this.isDaylightSaving,
      ziTimeRule: ziTimeRule ?? this.ziTimeRule,
    );
  }

  /// 验证基础信息是否完整
  bool get isBasicInfoValid {
    return name.isNotEmpty &&
           gender != null &&
           birthYear != null &&
           birthMonth != null &&
           birthDay != null &&
           birthHour != null;
  }

  /// 验证地址信息是否完整（如果启用了地址输入）
  bool get isLocationInfoValid {
    if (!enableLocationInput) return true;
    
    // 国内排盘需要省市县
    if (country == null || country == '中国') {
      return province != null && city != null && district != null;
    }
    
    // 国际排盘只需要国家和城市
    return country != null && city != null;
  }

  /// 表单是否完全有效
  bool get isValid => isBasicInfoValid && isLocationInfoValid;

  /// 清空表单数据
  void clear() {
    name = '';
    gender = null;
    calendarType = CalendarType.solar;
    birthYear = null;
    birthMonth = null;
    birthDay = null;
    birthHour = null;
    isLeapMonth = false;
    enableLocationInput = false;
    province = null;
    city = null;
    district = null;
    country = null;
    isDaylightSaving = false;
    ziTimeRule = ZiTimeRule.modern;
  }
}

/// 直接排盘数据模型
class DirectChartData {
  String? yearTianGan;
  String? yearDiZhi;
  String? monthTianGan;
  String? monthDiZhi;
  String? dayTianGan;
  String? dayDiZhi;
  String? hourTianGan;
  String? hourDiZhi;

  DirectChartData({
    this.yearTianGan,
    this.yearDiZhi,
    this.monthTianGan,
    this.monthDiZhi,
    this.dayTianGan,
    this.dayDiZhi,
    this.hourTianGan,
    this.hourDiZhi,
  });

  /// 复制并修改数据
  DirectChartData copyWith({
    String? yearTianGan,
    String? yearDiZhi,
    String? monthTianGan,
    String? monthDiZhi,
    String? dayTianGan,
    String? dayDiZhi,
    String? hourTianGan,
    String? hourDiZhi,
  }) {
    return DirectChartData(
      yearTianGan: yearTianGan ?? this.yearTianGan,
      yearDiZhi: yearDiZhi ?? this.yearDiZhi,
      monthTianGan: monthTianGan ?? this.monthTianGan,
      monthDiZhi: monthDiZhi ?? this.monthDiZhi,
      dayTianGan: dayTianGan ?? this.dayTianGan,
      dayDiZhi: dayDiZhi ?? this.dayDiZhi,
      hourTianGan: hourTianGan ?? this.hourTianGan,
      hourDiZhi: hourDiZhi ?? this.hourDiZhi,
    );
  }

  /// 验证是否所有字段都已填写
  bool get isValid {
    return yearTianGan != null &&
           yearDiZhi != null &&
           monthTianGan != null &&
           monthDiZhi != null &&
           dayTianGan != null &&
           dayDiZhi != null &&
           hourTianGan != null &&
           hourDiZhi != null;
  }

  /// 清空所有数据
  void clear() {
    yearTianGan = null;
    yearDiZhi = null;
    monthTianGan = null;
    monthDiZhi = null;
    dayTianGan = null;
    dayDiZhi = null;
    hourTianGan = null;
    hourDiZhi = null;
  }

  /// 获取完整的四柱八字字符串
  String get fullChart {
    if (!isValid) return '';
    return '$yearTianGan$yearDiZhi $monthTianGan$monthDiZhi $dayTianGan$dayDiZhi $hourTianGan$hourDiZhi';
  }
}
