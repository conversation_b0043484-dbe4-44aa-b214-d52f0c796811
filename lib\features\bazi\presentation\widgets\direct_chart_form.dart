import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';
import 'form_section.dart';
import 'custom_dropdown.dart';

/// 直接排盘表单组件
class DirectChartForm extends StatelessWidget {
  const DirectChartForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        final directData = provider.directChartData;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 说明信息
            _buildInstructions(context),
            
            const SizedBox(height: 24),
            
            // 四柱八字输入区域
            FormSection(
              title: '四柱八字输入',
              icon: Icons.grid_3x3_outlined,
              children: [
                _buildPillarInputs(context, provider, directData),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // 预览区域
            _buildPreview(context, directData),
          ],
        );
      },
    );
  }

  /// 构建说明信息
  Widget _buildInstructions(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '直接排盘说明',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '如果您已经知道准确的四柱八字，可以直接输入进行排盘分析。请按照年柱、月柱、日柱、时柱的顺序，分别选择对应的天干和地支。',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建四柱输入组件
  Widget _buildPillarInputs(BuildContext context, ChartProvider provider, directData) {
    final isMobile = ResponsiveUtils.isMobile(context);
    
    final pillars = [
      {
        'name': '年柱',
        'tianGan': directData.yearTianGan,
        'diZhi': directData.yearDiZhi,
        'onTianGanChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(yearTianGan: value));
        },
        'onDiZhiChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(yearDiZhi: value));
        },
        'tianGanError': provider.getFieldError('yearTianGan'),
        'diZhiError': provider.getFieldError('yearDiZhi'),
      },
      {
        'name': '月柱',
        'tianGan': directData.monthTianGan,
        'diZhi': directData.monthDiZhi,
        'onTianGanChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(monthTianGan: value));
        },
        'onDiZhiChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(monthDiZhi: value));
        },
        'tianGanError': provider.getFieldError('monthTianGan'),
        'diZhiError': provider.getFieldError('monthDiZhi'),
      },
      {
        'name': '日柱',
        'tianGan': directData.dayTianGan,
        'diZhi': directData.dayDiZhi,
        'onTianGanChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(dayTianGan: value));
        },
        'onDiZhiChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(dayDiZhi: value));
        },
        'tianGanError': provider.getFieldError('dayTianGan'),
        'diZhiError': provider.getFieldError('dayDiZhi'),
      },
      {
        'name': '时柱',
        'tianGan': directData.hourTianGan,
        'diZhi': directData.hourDiZhi,
        'onTianGanChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(hourTianGan: value));
        },
        'onDiZhiChanged': (String? value) {
          provider.updateDirectChartData(directData.copyWith(hourDiZhi: value));
        },
        'tianGanError': provider.getFieldError('hourTianGan'),
        'diZhiError': provider.getFieldError('hourDiZhi'),
      },
    ];

    if (isMobile) {
      // 移动端垂直布局
      return Column(
        children: pillars.map((pillar) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: _buildSinglePillar(context, pillar),
          );
        }).toList(),
      );
    } else {
      // 桌面端网格布局
      return Column(
        children: [
          Row(
            children: [
              Expanded(child: _buildSinglePillar(context, pillars[0])),
              const SizedBox(width: 16),
              Expanded(child: _buildSinglePillar(context, pillars[1])),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(child: _buildSinglePillar(context, pillars[2])),
              const SizedBox(width: 16),
              Expanded(child: _buildSinglePillar(context, pillars[3])),
            ],
          ),
        ],
      );
    }
  }

  /// 构建单个柱的输入组件
  Widget _buildSinglePillar(BuildContext context, Map<String, dynamic> pillar) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: theme.dividerColor),
        borderRadius: BorderRadius.circular(8),
        color: theme.colorScheme.surface,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 柱名称
          Text(
            pillar['name'],
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          // 天干选择
          CustomDropdown<String>(
            label: '天干',
            value: pillar['tianGan'],
            items: TianGan.allNames,
            onChanged: pillar['onTianGanChanged'],
            errorText: pillar['tianGanError'],
            isRequired: true,
          ),
          
          const SizedBox(height: 16),
          
          // 地支选择
          CustomDropdown<String>(
            label: '地支',
            value: pillar['diZhi'],
            items: DiZhi.allNames,
            onChanged: pillar['onDiZhiChanged'],
            errorText: pillar['diZhiError'],
            isRequired: true,
          ),
        ],
      ),
    );
  }

  /// 构建预览区域
  Widget _buildPreview(BuildContext context, directData) {
    final theme = Theme.of(context);
    final isComplete = directData.isValid;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.preview,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '八字预览',
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (isComplete) ...[
            // 完整八字显示
            Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.2),
                ),
              ),
              child: Text(
                directData.fullChart,
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 2,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ] else ...[
            // 未完成提示
            Text(
              '请完成所有四柱的天干地支选择',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
