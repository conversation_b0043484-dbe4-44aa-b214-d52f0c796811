import 'package:flutter/material.dart';
import '../../domain/models/chart_types.dart';

/// 子时规则选择器组件
class ZiTimeRuleSelector extends StatelessWidget {
  final ZiTimeRule value;
  final ValueChanged<ZiTimeRule> onChanged;
  final bool enabled;

  const ZiTimeRuleSelector({
    super.key,
    required this.value,
    required this.onChanged,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: ZiTimeRule.values.map((rule) {
        final isSelected = value == rule;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _ZiTimeRuleOption(
            rule: rule,
            isSelected: isSelected,
            onTap: enabled ? () => onChanged(rule) : null,
            theme: theme,
          ),
        );
      }).toList(),
    );
  }
}

/// 单个子时规则选项
class _ZiTimeRuleOption extends StatelessWidget {
  final ZiTimeRule rule;
  final bool isSelected;
  final VoidCallback? onTap;
  final ThemeData theme;

  const _ZiTimeRuleOption({
    required this.rule,
    required this.isSelected,
    required this.onTap,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected 
                ? theme.colorScheme.primary
                : theme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected 
              ? theme.colorScheme.primary.withOpacity(0.05)
              : theme.colorScheme.surface,
        ),
        child: Row(
          children: [
            // 单选按钮
            Radio<ZiTimeRule>(
              value: rule,
              groupValue: isSelected ? rule : null,
              onChanged: onTap != null ? (_) => onTap!() : null,
              activeColor: theme.colorScheme.primary,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
            
            const SizedBox(width: 12),
            
            // 规则信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 规则名称
                  Text(
                    rule.displayName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected 
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // 规则描述
                  Text(
                    rule.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
