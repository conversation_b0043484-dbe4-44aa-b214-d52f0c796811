import 'package:flutter/material.dart';
import '../../domain/models/chart_types.dart';
import '../../domain/models/chart_form_data.dart';

/// 排盘页面状态管理
class ChartProvider extends ChangeNotifier {
  // 当前选择的排盘类型
  ChartType _currentChartType = ChartType.bazi;
  
  // 当前选择的排盘模式（仅对八字排盘有效）
  ChartMode _currentChartMode = ChartMode.domestic;
  
  // 表单数据
  ChartFormData _formData = ChartFormData();
  
  // 直接排盘数据
  DirectChartData _directChartData = DirectChartData();
  
  // 表单验证错误信息
  Map<String, String> _validationErrors = {};
  
  // 是否正在提交
  bool _isSubmitting = false;

  // Getters
  ChartType get currentChartType => _currentChartType;
  ChartMode get currentChartMode => _currentChartMode;
  ChartFormData get formData => _formData;
  DirectChartData get directChartData => _directChartData;
  Map<String, String> get validationErrors => _validationErrors;
  bool get isSubmitting => _isSubmitting;

  /// 切换排盘类型
  void setChartType(ChartType type) {
    if (_currentChartType != type) {
      _currentChartType = type;
      _clearAllData();
      notifyListeners();
    }
  }

  /// 切换排盘模式
  void setChartMode(ChartMode mode) {
    if (_currentChartMode != mode) {
      _currentChartMode = mode;
      _clearFormData();
      notifyListeners();
    }
  }

  /// 更新表单数据
  void updateFormData(ChartFormData newData) {
    _formData = newData;
    _clearValidationErrors();
    notifyListeners();
  }

  /// 更新直接排盘数据
  void updateDirectChartData(DirectChartData newData) {
    _directChartData = newData;
    _clearValidationErrors();
    notifyListeners();
  }

  /// 验证表单数据
  bool validateForm() {
    _validationErrors.clear();

    // 验证姓名
    if (_formData.name.trim().isEmpty) {
      _validationErrors['name'] = '请输入姓名';
    }

    // 验证性别
    if (_formData.gender == null) {
      _validationErrors['gender'] = '请选择性别';
    }

    // 验证出生年份
    if (_formData.birthYear == null) {
      _validationErrors['birthYear'] = '请输入出生年份';
    } else if (_formData.birthYear! < 1900 || _formData.birthYear! > DateTime.now().year) {
      _validationErrors['birthYear'] = '年份范围应在1900-${DateTime.now().year}之间';
    }

    // 验证出生月份
    if (_formData.birthMonth == null) {
      _validationErrors['birthMonth'] = '请输入出生月份';
    } else if (_formData.birthMonth! < 1 || _formData.birthMonth! > 12) {
      _validationErrors['birthMonth'] = '月份范围应在1-12之间';
    }

    // 验证出生日期
    if (_formData.birthDay == null) {
      _validationErrors['birthDay'] = '请输入出生日期';
    } else if (_formData.birthDay! < 1 || _formData.birthDay! > 31) {
      _validationErrors['birthDay'] = '日期范围应在1-31之间';
    }

    // 验证出生时辰
    if (_formData.birthHour == null) {
      _validationErrors['birthHour'] = '请输入出生时辰';
    } else if (_formData.birthHour! < 0 || _formData.birthHour! > 23) {
      _validationErrors['birthHour'] = '时辰范围应在0-23之间';
    }

    // 验证地址信息（如果启用）
    if (_formData.enableLocationInput) {
      if (_currentChartMode == ChartMode.domestic) {
        if (_formData.province == null || _formData.province!.isEmpty) {
          _validationErrors['province'] = '请选择省份';
        }
        if (_formData.city == null || _formData.city!.isEmpty) {
          _validationErrors['city'] = '请选择城市';
        }
        if (_formData.district == null || _formData.district!.isEmpty) {
          _validationErrors['district'] = '请选择县区';
        }
      } else if (_currentChartMode == ChartMode.international) {
        if (_formData.country == null || _formData.country!.isEmpty) {
          _validationErrors['country'] = '请选择国家';
        }
        if (_formData.city == null || _formData.city!.isEmpty) {
          _validationErrors['city'] = '请选择城市';
        }
      }
    }

    notifyListeners();
    return _validationErrors.isEmpty;
  }

  /// 验证直接排盘数据
  bool validateDirectChart() {
    _validationErrors.clear();

    if (_directChartData.yearTianGan == null) {
      _validationErrors['yearTianGan'] = '请选择年柱天干';
    }
    if (_directChartData.yearDiZhi == null) {
      _validationErrors['yearDiZhi'] = '请选择年柱地支';
    }
    if (_directChartData.monthTianGan == null) {
      _validationErrors['monthTianGan'] = '请选择月柱天干';
    }
    if (_directChartData.monthDiZhi == null) {
      _validationErrors['monthDiZhi'] = '请选择月柱地支';
    }
    if (_directChartData.dayTianGan == null) {
      _validationErrors['dayTianGan'] = '请选择日柱天干';
    }
    if (_directChartData.dayDiZhi == null) {
      _validationErrors['dayDiZhi'] = '请选择日柱地支';
    }
    if (_directChartData.hourTianGan == null) {
      _validationErrors['hourTianGan'] = '请选择时柱天干';
    }
    if (_directChartData.hourDiZhi == null) {
      _validationErrors['hourDiZhi'] = '请选择时柱地支';
    }

    notifyListeners();
    return _validationErrors.isEmpty;
  }

  /// 提交排盘请求
  Future<void> submitChart() async {
    _isSubmitting = true;
    notifyListeners();

    try {
      bool isValid = false;
      
      if (_currentChartMode == ChartMode.direct) {
        isValid = validateDirectChart();
      } else {
        isValid = validateForm();
      }

      if (!isValid) {
        return;
      }

      // TODO: 这里将来会调用实际的排盘API
      await Future.delayed(const Duration(seconds: 2));
      
      // 模拟成功
      debugPrint('排盘提交成功');
      
    } catch (e) {
      debugPrint('排盘提交失败: $e');
    } finally {
      _isSubmitting = false;
      notifyListeners();
    }
  }

  /// 重置表单
  void resetForm() {
    if (_currentChartMode == ChartMode.direct) {
      _directChartData.clear();
    } else {
      _formData.clear();
    }
    _clearValidationErrors();
    notifyListeners();
  }

  /// 清空所有数据
  void _clearAllData() {
    _formData.clear();
    _directChartData.clear();
    _clearValidationErrors();
  }

  /// 清空表单数据
  void _clearFormData() {
    _formData.clear();
    _clearValidationErrors();
  }

  /// 清空验证错误
  void _clearValidationErrors() {
    _validationErrors.clear();
  }

  /// 获取指定字段的错误信息
  String? getFieldError(String fieldName) {
    return _validationErrors[fieldName];
  }

  /// 检查指定字段是否有错误
  bool hasFieldError(String fieldName) {
    return _validationErrors.containsKey(fieldName);
  }
}
