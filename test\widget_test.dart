// 八字命理应用的基础测试
//
// 测试应用的基本功能和组件

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bazi_app/app/app.dart';

void main() {
  testWidgets('应用启动测试', (WidgetTester tester) async {
    // 构建应用并触发一帧
    await tester.pumpWidget(const App());

    // 等待应用初始化完成
    await tester.pumpAndSettle();

    // 验证应用标题
    expect(find.text('八字命理'), findsOneWidget);
  });
}
