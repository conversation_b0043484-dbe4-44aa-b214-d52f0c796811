import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/constants/colors.dart';
import '../../../../core/constants/dimensions.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_container.dart';
import '../providers/chart_provider.dart';
import 'chart_mode_tabs.dart';
import 'bazi_form_content.dart';

/// 增强版八字排盘布局组件
/// 采用现代化设计理念，提升用户体验
class EnhancedBaziLayout extends StatelessWidget {
  const EnhancedBaziLayout({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenType = ResponsiveUtils.getScreenType(context);
    
    return Container(
      decoration: BoxDecoration(
        gradient: _buildBackgroundGradient(theme),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            // 页面标题区域
            _buildPageHeader(context, theme),
            
            // 主要内容区域
            Expanded(
              child: _buildMainContent(context, screenType),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建背景渐变
  LinearGradient _buildBackgroundGradient(ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;
    
    if (isDark) {
      return const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          AppColors.darkBackgroundColor,
          Color(0xFF1E1E1E),
        ],
      );
    } else {
      return const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          AppColors.lightBackgroundColor,
          Color(0xFFFBF9F4),
        ],
      );
    }
  }

  /// 构建页面标题区域
  Widget _buildPageHeader(BuildContext context, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingXL,
        vertical: AppDimensions.paddingL,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(0.8),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 主标题
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.auto_awesome,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '八字命理排盘',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '传统智慧，现代解读',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // 排盘模式选择
          _buildModeSelector(context, theme),
        ],
      ),
    );
  }

  /// 构建模式选择器
  Widget _buildModeSelector(BuildContext context, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: const ChartModeTabs(),
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent(BuildContext context, ScreenType screenType) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        return ResponsiveContainer(
          maxWidth: _getContentMaxWidth(screenType),
          child: SingleChildScrollView(
            padding: EdgeInsets.all(_getContentPadding(screenType)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 表单内容卡片
                _buildFormCard(context),
                
                const SizedBox(height: AppDimensions.paddingXL),
                
                // 结果预览区域（如果有数据）
                if (provider.hasValidData) ...[
                  _buildResultPreview(context),
                  const SizedBox(height: AppDimensions.paddingXL),
                ],
                
                // 底部说明信息
                _buildFooterInfo(context),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建表单卡片
  Widget _buildFormCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.08),
            offset: const Offset(0, 4),
            blurRadius: 16,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.colorScheme.shadow.withOpacity(0.04),
            offset: const Offset(0, 1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: const BaziFormContent(),
      ),
    );
  }

  /// 构建结果预览区域
  Widget _buildResultPreview(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingXL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primary.withOpacity(0.05),
            theme.colorScheme.secondary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.preview,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.paddingS),
              Text(
                '排盘预览',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            '您的八字信息已准备就绪，点击"开始排盘"按钮进行详细分析。',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部说明信息
  Widget _buildFooterInfo(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.primary,
                size: 16,
              ),
              const SizedBox(width: AppDimensions.paddingS),
              Text(
                '温馨提示',
                style: theme.textTheme.labelMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            '八字命理仅供参考，人生路径由自己把握。请理性对待命理分析结果，积极面对生活。',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取内容最大宽度
  double _getContentMaxWidth(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return double.infinity;
      case ScreenType.tablet:
        return 800;
      case ScreenType.desktop:
        return 1000;
      case ScreenType.largeDesktop:
        return 1200;
    }
  }

  /// 获取内容内边距
  double _getContentPadding(ScreenType screenType) {
    switch (screenType) {
      case ScreenType.mobile:
        return AppDimensions.paddingL;
      case ScreenType.tablet:
        return AppDimensions.paddingXL;
      case ScreenType.desktop:
        return AppDimensions.paddingXXL;
      case ScreenType.largeDesktop:
        return AppDimensions.paddingXXXL;
    }
  }
}
