import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';

/// 响应式工具类
class ResponsiveUtils {
  ResponsiveUtils._();

  /// 判断是否为移动端
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }

  /// 判断是否为平板
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.mobileBreakpoint && 
           width < AppConstants.tabletBreakpoint;
  }

  /// 判断是否为桌面端
  static bool isDesktop(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.tabletBreakpoint &&
           width < AppConstants.largeDesktopBreakpoint;
  }

  /// 判断是否为大屏桌面
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= AppConstants.largeDesktopBreakpoint;
  }

  /// 获取屏幕类型枚举
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < AppConstants.mobileBreakpoint) {
      return ScreenType.mobile;
    } else if (width < AppConstants.tabletBreakpoint) {
      return ScreenType.tablet;
    } else if (width < AppConstants.desktopBreakpoint) {
      return ScreenType.desktop;
    } else {
      return ScreenType.largeDesktop;
    }
  }

  /// 根据屏幕尺寸返回不同的值
  static T responsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    final screenType = getScreenType(context);
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
      case ScreenType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
    }
  }

  /// 获取导航栏宽度
  static double getNavigationWidth(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 0, // 移动端使用底部导航
      tablet: 64, // 平板使用紧凑导航
      desktop: 200, // 桌面使用标准导航
      largeDesktop: 280, // 大屏使用扩展导航
    );
  }

  /// 获取内容最大宽度 - 优化版本
  static double getContentMaxWidth(BuildContext context) {
    return responsiveValue(
      context,
      mobile: double.infinity,
      tablet: 768,
      desktop: 1024,
      largeDesktop: 1280,
    );
  }

  /// 获取卡片边距
  static double getCardMargin(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
      largeDesktop: 32,
    );
  }

  /// 获取网格列数
  static int getGridColumns(BuildContext context) {
    return responsiveValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4,
    );
  }

  /// 获取对话框宽度
  static double getDialogWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return responsiveValue(
      context,
      mobile: screenWidth * 0.9,
      tablet: 500,
      desktop: 600,
      largeDesktop: 700,
    );
  }

  /// 计算消息气泡最大宽度
  static double getMessageBubbleMaxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    return responsiveValue(
      context,
      mobile: screenWidth * 0.8,
      tablet: 400,
      desktop: 500,
      largeDesktop: 600,
    );
  }

  /// 获取安全区域内边距
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: isMobile(context) ? mediaQuery.padding.left : 0,
      right: isMobile(context) ? mediaQuery.padding.right : 0,
    );
  }
}

/// 屏幕类型枚举
enum ScreenType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}