import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../core/widgets/platform/custom_title_bar.dart';
import '../../../../shared/utils/responsive_utils.dart';
import '../../../../shared/widgets/responsive_container.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';
import '../widgets/chart_type_tabs.dart';
import '../widgets/bazi_chart_content.dart';
import '../widgets/enhanced_bazi_layout.dart';

/// 八字排盘页面
class BaziChartPage extends StatelessWidget {
  const BaziChartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ChartProvider(),
      child: const _BaziChartPageContent(),
    );
  }
}

class _BaziChartPageContent extends StatelessWidget {
  const _BaziChartPageContent();

  @override
  Widget build(BuildContext context) {
    // 小屏幕需要自己的标题栏，大屏幕由MainShell提供
    if (ResponsiveUtils.isMobile(context)) {
      return const Scaffold(
        appBar: CustomTitleBar(
          title: '八字命理 - 排盘推算',
          showBackButton: false,
        ),
        body: _BaziContent(),
      );
    } else {
      return const _BaziContent();
    }
  }
}

class _BaziContent extends StatelessWidget {
  const _BaziContent();

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartProvider>(
      builder: (context, provider, child) {
        switch (provider.currentChartType) {
          case ChartType.bazi:
            // 使用新的增强布局
            return const EnhancedBaziLayout();
          default:
            // 其他排盘类型使用原有布局
            return ResponsiveContainer(
              child: Column(
                children: [
                  // 排盘类型选择标签页
                  ResponsiveCard(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: const ChartTypeTabs(),
                  ),

                  // 主要内容区域
                  Expanded(
                    child: _buildComingSoonContent(context, provider.currentChartType),
                  ),
                ],
              ),
            );
        }
      },
    );
  }

  /// 构建"正在开发中"的占位内容
  Widget _buildComingSoonContent(BuildContext context, ChartType chartType) {
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 64,
            color: theme.colorScheme.primary.withOpacity(0.6),
          ),
          const SizedBox(height: 16),
          Text(
            chartType.displayName,
            style: theme.textTheme.headlineMedium,
          ),
          const SizedBox(height: 8),
          Text(
            '功能正在开发中，敬请期待...',
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}