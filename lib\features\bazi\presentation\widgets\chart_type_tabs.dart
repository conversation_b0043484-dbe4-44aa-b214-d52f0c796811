import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../domain/models/chart_types.dart';
import '../providers/chart_provider.dart';

/// 排盘类型选择标签页组件
class ChartTypeTabs extends StatelessWidget {
  const ChartTypeTabs({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Consumer<ChartProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: ChartType.values.map((chartType) {
                final isSelected = provider.currentChartType == chartType;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: _ChartTypeTab(
                    chartType: chartType,
                    isSelected: isSelected,
                    onTap: () => provider.setChartType(chartType),
                  ),
                );
              }).toList(),
            ),
          );
        },
      ),
    );
  }
}

/// 单个排盘类型标签
class _ChartTypeTab extends StatelessWidget {
  final ChartType chartType;
  final bool isSelected;
  final VoidCallback onTap;

  const _ChartTypeTab({
    required this.chartType,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected 
              ? theme.colorScheme.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected 
              ? Border.all(color: theme.colorScheme.primary, width: 1)
              : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getChartTypeIcon(chartType),
              size: 24,
              color: isSelected 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            const SizedBox(height: 4),
            Text(
              chartType.displayName,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withOpacity(0.8),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取排盘类型对应的图标
  IconData _getChartTypeIcon(ChartType chartType) {
    switch (chartType) {
      case ChartType.bazi:
        return Icons.calendar_month;
      case ChartType.liuyao:
        return Icons.hexagon_outlined;
      case ChartType.ziwei:
        return Icons.star_outline;
      case ChartType.qimen:
        return Icons.grid_3x3_outlined;
      case ChartType.liuren:
        return Icons.circle_outlined;
    }
  }
}
