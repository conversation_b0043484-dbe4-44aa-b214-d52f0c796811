import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/constants/colors.dart';
import 'theme_constants.dart';

/// 应用主题定义
class AppTheme {
  AppTheme._();

  /// 亮色主题 - 优化版本
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: AppColors.lightAccentColor,
    colorScheme: ThemeConstants.lightColorScheme,
    scaffoldBackgroundColor: AppColors.lightBackgroundColor,

    // AppBar主题
    appBarTheme: _buildAppBarTheme(isLight: true),

    // 文本主题 - 使用新的字体系统
    textTheme: _buildTextTheme(isLight: true),

    // 输入框主题
    inputDecorationTheme: _buildInputDecorationTheme(isLight: true),

    // 按钮主题
    elevatedButtonTheme: _buildElevatedButtonTheme(isLight: true),
    filledButtonTheme: _buildFilledButtonTheme(isLight: true),
    outlinedButtonTheme: _buildOutlinedButtonTheme(isLight: true),
    textButtonTheme: _buildTextButtonTheme(isLight: true),

    // 卡片主题
    cardTheme: _buildCardTheme(isLight: true),

    // 复选框主题
    checkboxTheme: _buildCheckboxTheme(isLight: true),

    // 单选框主题
    radioTheme: _buildRadioTheme(isLight: true),

    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: true),

    // 底部导航栏主题
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(isLight: true),
    
    // 对话框主题
    dialogTheme: _buildDialogTheme(isLight: true),
    
    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: true),
    
    // 浮动操作按钮主题
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(),
    
    // 图标主题
    iconTheme: _buildIconTheme(isLight: true),
    
    // 列表瓦片主题
    listTileTheme: _buildListTileTheme(isLight: true),
  );

  /// 暗色主题 - 优化版本
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: AppColors.darkAccentColor,
    colorScheme: ThemeConstants.darkColorScheme,
    scaffoldBackgroundColor: AppColors.darkBackgroundColor,

    // AppBar主题
    appBarTheme: _buildAppBarTheme(isLight: false),

    // 文本主题 - 使用新的字体系统
    textTheme: _buildTextTheme(isLight: false),

    // 输入框主题
    inputDecorationTheme: _buildInputDecorationTheme(isLight: false),

    // 按钮主题
    elevatedButtonTheme: _buildElevatedButtonTheme(isLight: false),
    filledButtonTheme: _buildFilledButtonTheme(isLight: false),
    outlinedButtonTheme: _buildOutlinedButtonTheme(isLight: false),
    textButtonTheme: _buildTextButtonTheme(isLight: false),

    // 卡片主题
    cardTheme: _buildCardTheme(isLight: false),

    // 复选框主题
    checkboxTheme: _buildCheckboxTheme(isLight: false),

    // 单选框主题
    radioTheme: _buildRadioTheme(isLight: false),

    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: false),

    // 底部导航栏主题
    bottomNavigationBarTheme: _buildBottomNavigationBarTheme(isLight: false),
    
    // 对话框主题
    dialogTheme: _buildDialogTheme(isLight: false),
    
    // 分割线主题
    dividerTheme: _buildDividerTheme(isLight: false),
    
    // 浮动操作按钮主题
    floatingActionButtonTheme: _buildFloatingActionButtonTheme(),
    
    // 图标主题
    iconTheme: _buildIconTheme(isLight: false),
    
    // 列表瓦片主题
    listTileTheme: _buildListTileTheme(isLight: false),
  );

  // AppBar主题构建
  static AppBarTheme _buildAppBarTheme({required bool isLight}) {
    return AppBarTheme(
      color: isLight ? AppColors.lightBackgroundColor : AppColors.darkBackgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      ),
      titleTextStyle: GoogleFonts.notoSansSc(
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
        fontSize: ThemeConstants.fontSizeXXL,
        fontWeight: ThemeConstants.fontWeightBold,
      ),
      centerTitle: true,
      systemOverlayStyle: isLight ? 
        SystemUiOverlayStyle.dark : 
        SystemUiOverlayStyle.light,
    );
  }

  // 文本主题构建 - 使用新的字体系统
  static TextTheme _buildTextTheme({required bool isLight}) {
    final primaryColor = isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText;
    final secondaryColor = isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText;
    final tertiaryColor = isLight ? AppColors.lightTertiaryText : AppColors.darkTertiaryText;

    return GoogleFonts.notoSansScTextTheme(
      TextTheme(
        // 展示级别文字
        displayLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeDisplayLarge,
          fontWeight: ThemeConstants.fontWeightBold,
          color: primaryColor,
          height: ThemeConstants.lineHeightTight,
          letterSpacing: ThemeConstants.letterSpacingTight,
        ),
        displayMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeDisplay,
          fontWeight: ThemeConstants.fontWeightBold,
          color: primaryColor,
          height: ThemeConstants.lineHeightTight,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        displaySmall: TextStyle(
          fontSize: ThemeConstants.fontSizeHeadline,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),

        // 标题级别文字
        headlineLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeTitleLarge,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        headlineMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeTitle,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        headlineSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeSubtitle,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),

        // 标题文字
        titleLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeBodyLarge,
          fontWeight: ThemeConstants.fontWeightSemiBold,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        titleMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeBody,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        titleSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeSmall,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),

        // 正文文字
        bodyLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeBodyLarge,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: primaryColor,
          height: ThemeConstants.lineHeightRelaxed,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        bodyMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeBody,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: secondaryColor,
          height: ThemeConstants.lineHeightRelaxed,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),
        bodySmall: TextStyle(
          fontSize: ThemeConstants.fontSizeSmall,
          fontWeight: ThemeConstants.fontWeightRegular,
          color: tertiaryColor,
          height: ThemeConstants.lineHeightRelaxed,
          letterSpacing: ThemeConstants.letterSpacingNormal,
        ),

        // 标签文字
        labelLarge: TextStyle(
          fontSize: ThemeConstants.fontSizeBody,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingWide,
        ),
        labelMedium: TextStyle(
          fontSize: ThemeConstants.fontSizeSmall,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: primaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingWide,
        ),
        labelSmall: TextStyle(
          fontSize: ThemeConstants.fontSizeCaption,
          fontWeight: ThemeConstants.fontWeightMedium,
          color: tertiaryColor,
          height: ThemeConstants.lineHeightNormal,
          letterSpacing: ThemeConstants.letterSpacingWide,
        ),
      ),
    );
  }

  // 输入框主题构建
  static InputDecorationTheme _buildInputDecorationTheme({required bool isLight}) {
    return InputDecorationTheme(
      filled: true,
      fillColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      contentPadding: const EdgeInsets.symmetric(vertical: 15.0, horizontal: 20.0),
      border: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: isLight ? ThemeConstants.lightBorderSide : ThemeConstants.darkBorderSide,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: isLight ? ThemeConstants.lightBorderSide : ThemeConstants.darkBorderSide,
      ),
      focusedBorder: const OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: ThemeConstants.focusedBorderSide,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: const BorderSide(color: AppColors.errorColor, width: 1.0),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: ThemeConstants.largeBorderRadius,
        borderSide: const BorderSide(color: AppColors.errorColor, width: 1.5),
      ),
      prefixIconColor: AppColors.lightAccentColor,
      suffixIconColor: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      hintStyle: TextStyle(
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
        fontSize: ThemeConstants.fontSizeM,
      ),
      labelStyle: TextStyle(
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
        fontSize: ThemeConstants.fontSizeM,
      ),
    );
  }

  // 其他主题构建方法...
  static ElevatedButtonThemeData _buildElevatedButtonTheme() {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        foregroundColor: Colors.white,
        backgroundColor: AppColors.lightAccentColor,
        shape: const RoundedRectangleBorder(
          borderRadius: ThemeConstants.largeBorderRadius,
        ),
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
        elevation: 2,
        shadowColor: AppColors.lightAccentColor.withOpacity(0.4),
        textStyle: GoogleFonts.notoSansSc(
          fontSize: ThemeConstants.fontSizeL,
          fontWeight: ThemeConstants.fontWeightBold,
        ),
      ),
    );
  }

  static TextButtonThemeData _buildTextButtonTheme({required bool isLight}) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
        textStyle: GoogleFonts.notoSansSc(
          fontSize: ThemeConstants.fontSizeM,
          fontWeight: ThemeConstants.fontWeightRegular,
        ),
        shape: const RoundedRectangleBorder(
          borderRadius: ThemeConstants.mediumBorderRadius,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      ),
    );
  }

  static CardThemeData _buildCardTheme({required bool isLight}) {
    return CardThemeData(
      elevation: 1.5,
      color: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.extraLargeBorderRadius,
      ),
      shadowColor: Colors.black.withOpacity(isLight ? 0.08 : 0.3),
      margin: const EdgeInsets.all(8.0),
    );
  }

  static CheckboxThemeData _buildCheckboxTheme() {
    return CheckboxThemeData(
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.smallBorderRadius,
      ),
      fillColor: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.lightAccentColor;
        }
        return null;
      }),
    );
  }

  static BottomNavigationBarThemeData _buildBottomNavigationBarTheme({required bool isLight}) {
    return BottomNavigationBarThemeData(
      backgroundColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      selectedItemColor: AppColors.lightAccentColor,
      unselectedItemColor: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      elevation: 8,
      type: BottomNavigationBarType.fixed,
      selectedLabelStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeS,
        fontWeight: ThemeConstants.fontWeightMedium,
      ),
      unselectedLabelStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeS,
        fontWeight: ThemeConstants.fontWeightRegular,
      ),
    );
  }

  static DialogTheme _buildDialogTheme({required bool isLight}) {
    return DialogTheme(
      backgroundColor: isLight ? AppColors.lightSurfaceColor : AppColors.darkSurfaceColor,
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.extraLargeBorderRadius,
      ),
      elevation: 8,
      titleTextStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeXL,
        fontWeight: ThemeConstants.fontWeightBold,
        color: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      ),
      contentTextStyle: GoogleFonts.notoSansSc(
        fontSize: ThemeConstants.fontSizeM,
        fontWeight: ThemeConstants.fontWeightRegular,
        color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      ),
    );
  }

  static DividerThemeData _buildDividerTheme({required bool isLight}) {
    return DividerThemeData(
      color: isLight ? AppColors.lightBorderColor : AppColors.darkBorderColor,
      thickness: 1,
      space: 1,
    );
  }

  static FloatingActionButtonThemeData _buildFloatingActionButtonTheme() {
    return const FloatingActionButtonThemeData(
      backgroundColor: AppColors.lightAccentColor,
      foregroundColor: Colors.white,
      elevation: 6,
      focusElevation: 8,
      hoverElevation: 8,
      highlightElevation: 12,
      shape: CircleBorder(),
    );
  }

  static IconThemeData _buildIconTheme({required bool isLight}) {
    return IconThemeData(
      color: isLight ? AppColors.lightSecondaryText : AppColors.darkSecondaryText,
      size: 24,
    );
  }

  static ListTileThemeData _buildListTileTheme({required bool isLight}) {
    return ListTileThemeData(
      iconColor: isLight ? AppColors.lightAccentColor : AppColors.darkAccentColor,
      textColor: isLight ? AppColors.lightPrimaryText : AppColors.darkPrimaryText,
      tileColor: Colors.transparent,
      selectedTileColor: AppColors.lightAccentColor.withOpacity(0.1),
      shape: const RoundedRectangleBorder(
        borderRadius: ThemeConstants.mediumBorderRadius,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}